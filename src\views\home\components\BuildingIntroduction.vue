<template>
  <PageCard class="page-card" title="楼宇简介">
    <div class="building-intro">
      <ul>
        <li v-for="(ele, i) in arr" :key="i">
          <div class="keys">{{ ele.name }}</div>
          <div class="names">中国某公司{{ ele.value }}</div>
        </li>
      </ul>

      <!-- <div class="intro-content">
        <div class="intro-images">
          <div class="main-image">
            <img src="/example/introduce_1.png" alt="楼宇主图" />
          </div>
          <div class="sub-images">
            <img src="/example/introduce_2.png" alt="楼宇图2" />
            <img src="/example/introduce_3.png" alt="楼宇图3" />
            <img src="/example/introduce_4.png" alt="楼宇图4" />
          </div>
        </div>
        <div class="intro-text">
          <div class="intro-item">
            <span class="label">建筑面积：</span>
            <span class="value">{{ buildingInfo.area }}</span>
          </div>
          <div class="intro-item">
            <span class="label">楼层数量：</span>
            <span class="value">{{ buildingInfo.floors }}</span>
          </div>
          <div class="intro-item">
            <span class="label">入驻企业：</span>
            <span class="value">{{ buildingInfo.companies }}</span>
          </div>
          <div class="intro-item">
            <span class="label">建成时间：</span>
            <span class="value">{{ buildingInfo.buildTime }}</span>
          </div>
        </div>
      </div> -->
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import { ref } from "vue";
import PageCard from "@/components/PageCard.vue";
const arr = ref([
  {
    name: "开发商",
    value: "上海某公司",
  },
  {
    name: "物业管理单位",
    value: "上海某公司",
  },
  {
    name: "项目位置",
    value: "某某路",
  },
  {
    name: "总建筑面积",
    value: "123223㎡",
  },
  {
    name: "商业面积",
    value: "123223㎡",
  },
  {
    name: "写字楼面积",
    value: "123223㎡",
  },
  {
    name: "车位数量",
    value: "653",
  },
  {
    name: "楼宇业态",
    value: "甲级写字楼、商业",
  },
]);
// const buildingInfo = ref({
//   area: "50,000㎡",
//   floors: "18层",
//   companies: "120家",
//   buildTime: "2018年",
// });
</script>

<style scoped lang="less">
.page-card {
  width: 700px;
  height: 375px;
}
.building-intro {
  padding: 18px 0 0 30px;
  ul {
    color: #fff;
    font-size: 20px;
    line-height: 18px;
    li {
      display: flex;
      position: relative;
      height: 36px;
      &::before {
        content: "";
        position: absolute;
        left: -20px;
        top: 5px;
        width: 8px;
        height: 8px;
        background: #fff;
        display: inline-block;
        // border-radius: 2px; // 可选，略圆角
      }
      .keys {
        width: 30%;
        margin-bottom: 20px;
      }
      .names {
        flex: 1;
      }
    }
  }
  .intro-content {
    display: flex;
    gap: 40px;
    height: 100%;

    .intro-images {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .main-image {
        flex: 2;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 8px;
          border: 2px solid rgba(78, 237, 255, 0.3);
        }
      }

      .sub-images {
        flex: 1;
        display: flex;
        gap: 15px;

        img {
          flex: 1;
          height: 100%;
          object-fit: cover;
          border-radius: 6px;
          border: 1px solid rgba(78, 237, 255, 0.2);
        }
      }
    }

    .intro-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      padding: 20px 0;

      .intro-item {
        display: flex;
        align-items: center;
        margin-bottom: 30px;

        .label {
          // font-family: "HarmonyOS Sans SC";
          font-size: 36px;
          color: #4eedff;
          min-width: 180px;
          font-weight: 500;
        }

        .value {
          // font-family: "HarmonyOS Sans SC";
          font-size: 40px;
          color: #ffffff;
          font-weight: 600;
          text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
        }
      }
    }
  }
}
</style>
