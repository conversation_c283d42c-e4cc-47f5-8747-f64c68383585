<template>
  <div>
    <a-modal
      :visible="visible"
      title="电能详细流向"
      :destroyOnClose="true"
      @ok="handleOk"
      width="2560px"
      centered
      wrap-class-name="energy-consumpt-trends-detail-dialog"
      @cancel="handleOk"
    >
      <div class="details-dialog">
        <SearchForm :fields="searchModalFields" :initialValues="initialModalValues" />
        <div class="chart-container" v-if="showChart">
          <common-chart :echart-obj="chartModalOption" />
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import CommonChart from "@/components/CommonChart.vue";
import SearchForm from "@/components/SearchForm.vue";
import { ref, watch } from "vue";
import { getEnergyConsumptionTrendDetail } from "@/api/energy";
const props = defineProps<{ visible: boolean }>();
const searchModalFields = ref([
  {
    type: "select" as const,
    prop: "transformer",
    formItem: { label: "变压器" },
    attrs: {
      placeholder: "请选择",
      clearable: true,
      size: "large",
    },
    options: [
      { label: "AN101", value: "AN101" },
      { label: "AN201", value: "AN201" },
      { label: "AN301", value: "AN301" },
      { label: "AN401", value: "AN401" },
      { label: "AN501", value: "AN501" },
      { label: "AN601", value: "AN601" },
      { label: "AN701", value: "AN701" },
      { label: "AN801", value: "AN801" },
    ],
    on: {
      change: (val: any) => {
        initialModalValues.value.transformer = val;
        getCurrentData();
      },
    },
  },
  // {
  //   type: "select" as const,
  //   prop: "feedeLine",
  //   formItem: { label: "馈线" },
  //   attrs: {
  //     placeholder: "请选择",
  //     clearable: true,
  //     size: "large",
  //   },
  //   options: [
  //     { label: "全部", value: "" },
  //     { label: "ST1", value: "1" },
  //     { label: "ST2", value: "2" },
  //   ],
  //   on: {
  //     change: (val: any) => {
  //       initialModalValues.value.feedeLine = val;
  //       // getStatusList();
  //     },
  //   },
  // },
  {
    type: "radio" as const,
    prop: "timeModel",
    formItem: { label: "" },
    showRight: true,
    attrs: {
      size: "large",
    },
    options: [
      { label: "今日", value: "currentDay" },
      { label: "本月", value: "currentMonth" },
      { label: "本年", value: "currentYear" },
    ],
    on: {
      change: (val: any) => {
        initialModalValues.value.timeModel = val;
        getCurrentData();
      },
    },
  },
  // {
  //   type: "radio" as const,
  //   prop: "feedeLine",
  //   formItem: { label: "" },
  //   showRight: true,
  //   attrs: {
  //     size: "large",
  //   },
  //   options: [
  //     { label: "日", value: "currentDay" },
  //     { label: "月", value: "currentMonth" },
  //     { label: "年", value: "currentYear" },
  //   ],
  //   on: {
  //     change: (val: any) => {
  //       getStatusList();
  //     },
  //   },
  // },
]);
const initialModalValues = ref({
  timeModel: "currentDay",
  transformer: "AN101",
});
const showChart = ref(false);
let data: any;
const getCurrentData = async () => {
  const params = { ...initialModalValues.value };
  try {
    const res = await getEnergyConsumptionTrendDetail(params);
    if (res.code === 200) {
      data = res.data;
      // // 查看是否有 相同的 name, 有的话会报错。
      // data.names.forEach((item: any, index: number) => {
      //   if (data.names.filter((i: any) => i.name === item.name).length > 1) {
      //     console.log("%c Line:131 🌰 i", "color:#4fff4B", item);
      //     console.log("%c Line:130 🌽 index", "color:#b03734", index);
      //     item.name = item.name + index;
      //   }
      // });

      data.links.forEach((item: any, index: number) => {
        if (!item.value) item.value = 0;
        // 保留1位小数
        item.value = Number(item.value.toFixed(1));
      });
      chartModalOption.value.series[0].data = data.names;
      chartModalOption.value.series[0].links = data.links;
      showChart.value = true;
    } else {
      console.log(res);
    }
  } catch (error) {
    console.error("加载能耗趋势失败:", error);
  }
};

const chartModalOption = ref<any>({
  tooltip: {
    trigger: "item",
    triggerOn: "mousemove",
  },
  animation: false, // 关闭动画提升性能
  series: [
    {
      type: "sankey",
      top: "6%",
      bottom: "13%",
      left: "6%",
      right: "5%",
      layout: "none",
      orient: "vertical",
      label: {
        // rotate: -90,
        // color: "#fff",
        fontSize: 12,
        formatter: function (params: any) {
          // 1. 按字符拆分文字，用\n连接实现竖排
          const verticalText = params.name.split("").join("\n");
          // 2. 根据value值判断应用哪种样式
          if (params.data.status === "0") {
            return `{highlight|${verticalText}}`;
          } else {
            return `{normal|${verticalText}}`;
          }
        },
        rich: {
          normal: {
            color: "#fff",
          },
          highlight: {
            color: "red",
            fontWeight: "bold",
          },
        },
      },
      labelLayout(params: any) {
        return {
          y: params.rect.y + 25,
        };
      },
      nodeAlign: "left",
      nodeWidth: 20,
      nodeGap: 15,
      // layoutIterations: 32, // 减少布局迭代次数
      lineStyle: {
        color: "gradient",
        curveness: 0.5, // 减少曲率提升性能
      },
      // edgeLabel: {
      //   rotate: 90,
      // },
      data: [],
      links: [],
    },
  ],
});
const emit = defineEmits(["update:visible"]);
// 保持 handleOk 不变
const handleOk = () => {
  emit("update:visible", false);
};
// 监听 visible 的变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      getCurrentData();
    }
  },
);
// onMounted(() => {
//   getCurrentData();
// });
</script>

<style lang="less" scoped>
.chart-container {
  min-width: 3000px;

  height: 90%;
}
:deep(.ant-modal-content) {
  background: rgb(0, 26, 40, 0.8) !important;
  color: #fff;
}
.details-dialog {
  width: 100%;
  height: 80vh;
}
</style>

<style lang="less">
.energy-consumpt-trends-detail-dialog {
  .ant-modal {
    width: 5000px !important;
    max-height: 1350px;
  }
  .ant-modal-close {
    color: #fff;
  }
  .ant-modal-content,
  .ant-modal-header {
    background: rgb(0, 26, 40, 0.8);
    color: #fff;
    .ant-modal-title {
      color: #fff;
    }
  }
}
</style>
