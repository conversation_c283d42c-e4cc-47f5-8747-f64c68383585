.layout-content {
  height: 100%;
  width: 100%;
  margin: 0 auto;
  position: relative;
}
.ant-layout {
  background: transparent;
}
.flex {
  display: flex;
  flex-direction: row;
}
.flex-auto {
  flex: auto;
  min-height: 0;
}
.flex-column {
  flex-direction: column;
}
.justify-between {
  justify-content: space-between;
}
.flex-center {
  justify-content: center;
  align-items: center;
}
.flex-end {
  justify-content: flex-start;
  align-items: flex-end;
}
.flex-wrap {
  flex-wrap: wrap;
}
.align-center {
  align-items: center;
}
.no-padding {
  padding: 0;
}
.app-wrapper {
  position: relative;
  // font-family: "HarmonyOS Sans SC";
  font-style: normal;
  font-weight: 500;
  background: var(--wrapper-bg);
  .layout-content {
    display: flex;
    flex-direction: row;
    padding-top: 150px;
  }
  .app-main-container {
    pointer-events: none;
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
  }
  .content-main {
    background: transparent;
    width: 100%;
    height: 100%;
    z-index: 9;
    flex: auto;
  }
  .menu-header-container {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 11;
    // .menu-warpper {
    //   // background-image: url("../assets/layout/header.svg");
    //   width: 100%;
    //   margin: 0 auto;
    //   height: 236px;
    //   background-repeat: no-repeat;
    //   background-position: center;
    //   background-size: 100% 100%;
    //   .menu-title-container {
    //     width: 2590px;
    //     margin: 0 auto;
    //   }
    //   img {
    //     width: 112px;
    //     height: 175px;
    //     margin-right: 23px;
    //   }
    //   .menu-title {
    //     width: 2359px;
    //     color: #ffffff;
    //     text-shadow: 0px 9px 25px #002839;
    //     text-align: center;
    //     span {
    //       font-weight: 700;
    //       // font-size: 0.8vw;
    //       font-size: 120px;
    //       padding-top: 63px;
    //       line-height: 236px;
    //       text-align: center;
    //     }
    //   }
    // }
  }
  .tool-control-area-container {
    position: absolute;
    pointer-events: auto;
    bottom: 478px;
    // width: 3327px;
    left: 50%;
    transform: translateX(-50%);
  }

  .webrtc {
    height: 100%;
    width: 100%;
    position: relative;
    #overlay {
      -moz-border-radius-bottomright: 5px;
      -moz-border-radius-bottomleft: 5px;
      -webkit-border-bottom-right-radius: 5px;
      -webkit-border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;
      /* future proofing */
      border-bottom-left-radius: 5px;
      /* future proofing */
      -khtml-border-bottom-right-radius: 5px;
      /* for old Konqueror browsers */
      -khtml-border-bottom-left-radius: 5px;
      /* for old Konqueror browsers */

      -webkit-touch-callout: none;
      /* iOS Safari */
      -webkit-user-select: none;
      /* Safari */
      -khtml-user-select: none;
      /* Konqueror HTML */
      -moz-user-select: none;
      /* Firefox */
      -ms-user-select: none;
      /* Internet Explorer/Edge */
      user-select: none;
      /* Non-prefixed version, currently
                                      supported by Chrome and Opera */

      position: absolute;
      top: 0;
      right: 2%;
      z-index: 100;

      border-top-width: 0px;
    }
    .player-container {
      position: relative;
      height: 100%;
      width: 100%;
      padding: 0;
    }
  }
}
.focus-border {
  border: 3px solid #4eedff !important;
}
