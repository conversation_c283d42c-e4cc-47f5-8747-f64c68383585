<template>
  <PageCard title="园区设备">
    <div class="equipment-container">
      <div class="equipment-overview">
        <div class="overview-item">
          <div class="overview-icon">
            <!-- <img src="/icon/electricity_power.svg" alt="电力设备" /> -->
          </div>
          <div class="overview-info">
            <div class="overview-label">电力设备</div>
            <div class="overview-value">
              {{ equipmentData.power.total }}
              <span>个</span>
            </div>
          </div>
        </div>

        <div class="overview-item">
          <div class="overview-icon">
            <!-- <img src="/icon/electricity_consumption.svg" alt="空调设备" /> -->
          </div>
          <div class="overview-info">
            <div class="overview-label">空调设备</div>
            <div class="overview-value">
              {{ equipmentData.hvac.total }}
              <span>个</span>
            </div>
          </div>
        </div>
        <div class="overview-item">
          <div class="overview-icon">
            <!-- <img src="/icon/electricity_consumption.svg" alt="空调设备" /> -->
          </div>
          <div class="overview-info">
            <div class="overview-label">空调设备</div>
            <div class="overview-value">
              {{ equipmentData.hvac.total }}
              <span>个</span>
            </div>
          </div>
        </div>
        <div class="overview-item">
          <div class="overview-icon">
            <!-- <img src="/icon/electricity_consumption.svg" alt="空调设备" /> -->
          </div>
          <div class="overview-info">
            <div class="overview-label">空调设备</div>
            <div class="overview-value">
              {{ equipmentData.hvac.total }}
              <span>个</span>
            </div>
          </div>
        </div>
        <div class="overview-item">
          <div class="overview-icon">
            <!-- <img src="/icon/electricity_consumption.svg" alt="空调设备" /> -->
          </div>
          <div class="overview-info">
            <div class="overview-label">空调设备</div>
            <div class="overview-value">
              {{ equipmentData.hvac.total }}
              <span>个</span>
            </div>
          </div>
        </div>
        <div class="overview-item">
          <div class="overview-icon">
            <!-- <img src="/icon/electricity_consumption.svg" alt="空调设备" /> -->
          </div>
          <div class="overview-info">
            <div class="overview-label">空调设备</div>
            <div class="overview-value">
              {{ equipmentData.hvac.total }}
              <span>个</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import { ref } from "vue";
import PageCard from "@/components/PageCard.vue";

const equipmentData = ref({
  power: {
    total: 48,
    normal: 45,
    fault: 3,
  },
  hvac: {
    total: 32,
    normal: 30,
    fault: 2,
  },
});

// const equipmentColumns = ref([
//   { name: "设备名称", props: "name", width: "25%" },
//   { name: "设备类型", props: "type", width: "20%" },
//   { name: "运行状态", props: "status", width: "20%" },
//   { name: "位置", props: "location", width: "25%" },
//   { name: "操作", props: "operateList", width: "10%" },
// ]);

// const equipmentList = ref([
//   {
//     name: "配电柜-01",
//     type: "电力设备",
//     status: "正常运行",
//     location: "1F配电间",
//     operateList: [1, 2],
//     bimId: "power_001",
//   },
//   {
//     name: "空调主机-A1",
//     type: "空调设备",
//     status: "正常运行",
//     location: "屋顶机房",
//     operateList: [1, 2],
//     bimId: "hvac_001",
//   },
//   {
//     name: "电梯-01",
//     type: "电梯设备",
//     status: "维护中",
//     location: "A座电梯间",
//     operateList: [1, 3],
//     bimId: "elevator_001",
//   },
//   {
//     name: "消防泵-01",
//     type: "消防设备",
//     status: "正常运行",
//     location: "地下室泵房",
//     operateList: [1, 2],
//     bimId: "fire_001",
//   },
//   {
//     name: "照明控制器-01",
//     type: "照明设备",
//     status: "故障",
//     location: "2F走廊",
//     operateList: [1, 4],
//     bimId: "light_001",
//   },
// ]);
</script>

<style scoped lang="less">
.page-card {
  width: 704px;
  height: 400px;
}
.equipment-container {
  padding: 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 30px;

  .equipment-overview {
    display: grid;
    grid-template-rows: repeat(2, 1fr);
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;

    .overview-item {
      display: flex;
      align-items: center;
      // justify-content: space-between;
      // font-size: 24px;
      // gap: 20px;
      padding: 20px 10px;
      background: rgba(0, 26, 40, 0.3);
      border: 2px solid rgba(78, 237, 255, 0.2);
      border-radius: 12px;
      .overview-icon {
        width: 50px;
        height: 50px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(78, 237, 255, 0.1);
        border-radius: 50%;

        img {
          width: 30px;
          height: 30px;
          // margin-right: 12px;
          // filter: brightness(1.2);
        }
      }

      .overview-info {
        .overview-value {
          // font-family: "HarmonyOS Sans SC";
          font-size: 24px;
          font-weight: 700;
          color: #4eedff;
          line-height: 1;
          margin-bottom: 8px;
          span {
            font-size: 16px;
            color: #fff;
          }
        }

        .overview-label {
          // font-family: "HarmonyOS Sans SC";
          font-size: 20px;
          color: #ffffff;
          margin-bottom: 10px;
        }

        .overview-status {
          display: flex;
          gap: 15px;

          span {
            // font-family: "HarmonyOS Sans SC";
            font-size: 22px;

            &.normal {
              color: #52c41a;
            }

            &.fault {
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }
}
</style>
