# UE 监听器简单使用指南

## 🎯 简介

这是一个简单易懂的 UE.interface 监听系统，让你可以在 Vue 组件中轻松监听 UE 发送的所有方法。

## 🚀 快速开始

### 1. 在组件中使用（推荐方式）

```vue
<template>
  <div>
    <p>当前设备: {{ deviceName }}</p>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { createCommonListeners } from "@/composables/useUEListener";

const deviceName = ref("");

// 创建监听器
const listeners = createCommonListeners();

// 监听设备详情
listeners.onDeviceDetail((data) => {
  console.log("收到设备数据:", data);
  deviceName.value = data.deviceName;
});

// 监听页面信息
listeners.onInfoPage((data) => {
  console.log("收到页面信息:", data);
});
</script>
```

### 2. 自定义监听器

```vue
<script setup>
import { useUEListener } from "@/composables/useUEListener";

const { addListener } = useUEListener();

// 监听任何 UE 方法
addListener("YourMethodName", (data) => {
  console.log("收到数据:", data);
  // 处理数据
});
</script>
```

## 📋 常用监听器

### 预定义的监听器

```javascript
const listeners = createCommonListeners();

// 1. 页面信息
listeners.onInfoPage((data) => {
  console.log("页面信息:", data);
});

// 2. 设备详情
listeners.onDeviceDetail((data) => {
  console.log("设备详情:", data);
});

// 3. 页面切换
listeners.onPageSwitch((data) => {
  console.log("页面切换:", data);
});

// 4. 设备状态
listeners.onDeviceStatus((data) => {
  console.log("设备状态:", data);
});

// 5. 告警信息
listeners.onAlarm((data) => {
  console.log("告警信息:", data);
});

// 6. 实时数据
listeners.onRealTimeData((data) => {
  console.log("实时数据:", data);
});

// 7. 点击事件
listeners.onObjectClick((data) => {
  console.log("对象点击:", data);
});

// 8. 自定义方法
listeners.onCustom("MyMethod", (data) => {
  console.log("自定义方法:", data);
});
```

## 🔧 测试方法

在浏览器控制台中测试：

```javascript
// 测试 InfoPage
window.ueInterface.InfoPage({ message: "hello" });

// 测试设备详情
window.ueInterface.DeviceDetail({
  deviceId: "device_001",
  deviceName: "测试设备",
});

// 测试自定义方法
window.ueInterface.MyCustomMethod({ data: "test" });
```

## 📝 完整示例

```vue
<template>
  <div>
    <h3>UE 数据监听</h3>
    <div v-for="item in dataList" :key="item.id">{{ item.method }}: {{ item.data }}</div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { createCommonListeners, useUEListener } from "@/composables/useUEListener";

const dataList = ref([]);

// 使用预定义监听器
const listeners = createCommonListeners();

// 监听多个方法
listeners.onInfoPage((data) => {
  dataList.value.push({
    id: Date.now(),
    method: "InfoPage",
    data: JSON.stringify(data),
  });
});

listeners.onDeviceDetail((data) => {
  dataList.value.push({
    id: Date.now(),
    method: "DeviceDetail",
    data: JSON.stringify(data),
  });
});

// 使用自定义监听器
const { addListener } = useUEListener();

addListener("CustomData", (data) => {
  dataList.value.push({
    id: Date.now(),
    method: "CustomData",
    data: JSON.stringify(data),
  });
});
</script>
```

## ⚠️ 注意事项

1. **自动清理**: 组件卸载时会自动清理监听器，无需手动清理
2. **错误处理**: 监听器执行错误不会影响其他监听器
3. **数据格式**: UE 发送的数据可能是字符串，需要适当解析
4. **方法命名**: 确保监听器名称与 UE 端调用的方法名一致

## 🐛 调试技巧

```javascript
// 查看所有已注册的监听器
console.log(window.ueListeners);

// 查看 UE 接口
console.log(window.ueInterface);

// 手动触发测试
window.ueInterface.YourMethod({ test: "data" });
```

## 📁 文件说明

- `public/UE.js` - 监听器系统核心代码
- `src/composables/useUEListener.ts` - Vue 组合式函数
- `src/examples/ue-listener-simple.vue` - 完整使用示例
- `src/shims-vue.d.ts` - TypeScript 类型声明

这样你就可以在任何 Vue 组件中轻松监听 UE 的所有方法了！
