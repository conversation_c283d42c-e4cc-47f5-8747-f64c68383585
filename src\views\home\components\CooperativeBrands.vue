<template>
  <PageCard title="合作品牌" class="page-card">
    <div class="brands-container">
      <div class="brands-grid">
        <div
          v-for="(brand, index) in brands"
          :key="index"
          class="brand-item"
          :class="{ active: brand.active }"
        >
          <div class="brand-logo">
            <img :src="brand.logo" :alt="brand.name" />
          </div>
          <!-- <div class="brand-name">{{ brand.name }}</div> -->
        </div>
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import { ref } from "vue";
import PageCard from "@/components/PageCard.vue";

const brands = ref([
  {
    name: "华为",
    // logo: '/example/tech_1.png',
    active: true,
  },
  {
    name: "阿里云",
    // logo: '/example/tech_2.png',
    active: false,
  },
  {
    name: "腾讯云",
    // logo: '/example/tech_3.png',
    active: true,
  },
  {
    name: "百度",
    // logo: '/example/tech_4.png',
    active: false,
  },
  {
    name: "京东",
    // logo: '/example/tech_5.png',
    active: true,
  },
  {
    name: "小米",
    // logo: '/example/tech_6.png',
    active: false,
  },
  {
    name: "字节跳动",
    // logo: '/example/tech_7.png',
    active: true,
  },
  {
    name: "美团",
    // logo: '/example/tech_8.png',
    active: false,
  },
]);
</script>

<style scoped lang="less">
.page-card {
  width: 580px;
  height: 375px;
}
.brands-container {
  padding: 30px;

  .brands-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 10px;
    height: 100%;

    .brand-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
      border: 2px solid rgba(78, 237, 255, 0.2);
      border-radius: 12px;
      background: rgba(0, 26, 40, 0.3);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        border-color: rgba(78, 237, 255, 0.6);
        background: rgba(0, 26, 40, 0.5);
        transform: translateY(-2px);
      }

      &.active {
        border-color: #4eedff;
        background: rgba(78, 237, 255, 0.1);
        box-shadow: 0 0 20px rgba(78, 237, 255, 0.3);

        .brand-name {
          color: #4eedff;
        }
      }

      .brand-logo {
        width: 40px;
        height: 40px;
        // margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          max-width: 100%;
          width: 100%;
          max-height: 100%;
          object-fit: contain;
          filter: brightness(0.9);
        }
      }

      .brand-name {
        // font-family: "HarmonyOS Sans SC";
        font-size: 28px;
        color: #ffffff;
        font-weight: 500;
        text-align: center;
        transition: color 0.3s ease;
      }
    }
  }
}
</style>
