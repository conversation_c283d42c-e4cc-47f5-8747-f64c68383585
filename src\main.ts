import { createApp } from "vue";
import mitt from "mitt";
import Antd from "ant-design-vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "@/utils/rem";
import DeviceModalPlugin from "@/utils/deviceModal";
import "ant-design-vue/dist/antd.css";
import "animate.css";
import "@/style/animate.less";
import "@/style/antdCustom.less";
import "@/style/common.less";
import "@/style/normal.less";
import "@/style/theme.less";
import "vue3-carousel-3d/dist/index.css";
// import "@/style/page.less";
// import "@/assets/air/qweather-icons.css";
// import "@/style/custom.less";
const bus = mitt();
const app = createApp(App);

app.config.globalProperties.$bus = bus;

app.use(store).use(router).use(Antd).use(DeviceModalPlugin).mount("#app");
