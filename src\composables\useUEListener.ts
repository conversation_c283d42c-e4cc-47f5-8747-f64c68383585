/**
 * UE 监听器组合式函数
 * 简单易用的 UE.interface 监听器
 */

import { onUnmounted } from "vue";

// 存储当前组件的监听器，用于自动清理
const componentListeners: Array<{
  methodName: string;
  callback: (data: any) => void;
}> = [];

/**
 * 使用 UE 监听器的组合式函数
 */
export function useUEListener() {
  /**
   * 添加 UE 监听器
   * @param methodName UE 调用的方法名
   * @param callback 回调函数
   */
  const addListener = (methodName: string, callback: (data: any) => void) => {
    // 检查全局方法是否存在
    if (!window.addUEListener) {
      console.error("UE 监听器系统未初始化，请确保 UE.js 已加载");
      return;
    }

    // 添加到全局监听器
    window.addUEListener(methodName, callback);

    // 记录到当前组件的监听器列表
    componentListeners.push({ methodName, callback });

    console.log(`✅ 已添加监听器: ${methodName}`);
  };

  /**
   * 移除 UE 监听器
   * @param methodName 方法名
   * @param callback 回调函数
   */
  const removeListener = (methodName: string, callback: (data: any) => void) => {
    if (!window.removeUEListener) {
      console.error("UE 监听器系统未初始化");
      return;
    }

    window.removeUEListener(methodName, callback);

    // 从组件监听器列表中移除
    const index = componentListeners.findIndex(
      (item) => item.methodName === methodName && item.callback === callback,
    );
    if (index > -1) {
      componentListeners.splice(index, 1);
    }

    console.log(`❌ 已移除监听器: ${methodName}`);
  };

  /**
   * 组件卸载时自动清理所有监听器
   */
  onUnmounted(() => {
    componentListeners.forEach(({ methodName, callback }) => {
      if (window.removeUEListener) {
        window.removeUEListener(methodName, callback);
      }
    });
    componentListeners.length = 0; // 清空数组
    console.log("🧹 已清理所有 UE 监听器");
  });

  return {
    addListener,
    removeListener,
  };
}

/**
 * 快速创建常用监听器的辅助函数
 */
export function createCommonListeners() {
  const { addListener } = useUEListener();

  return {
    // 监听设备点击 InfoPage
    onInfoPage: (callback: (data: any) => void) => {
      addListener("InfoPage", callback);
    },

    // // 监听设备详情
    // onDeviceDetail: (callback: (data: any) => void) => {
    //   addListener("DeviceDetail", callback);
    // },

    // // 监听页面切换
    // onPageSwitch: (callback: (data: any) => void) => {
    //   addListener("PageSwitch", callback);
    // },
  };
}
