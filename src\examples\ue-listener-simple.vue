<template>
  <div class="ue-listener-demo">
    <h2>UE 监听器示例</h2>
    
    <div class="demo-section">
      <h3>接收到的数据：</h3>
      <div class="data-display">
        <div v-for="(item, index) in receivedData" :key="index" class="data-item">
          <strong>{{ item.method }}:</strong> {{ item.data }}
          <small>{{ item.time }}</small>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h3>测试按钮：</h3>
      <a-space>
        <a-button @click="testInfoPage">测试 InfoPage</a-button>
        <a-button @click="testDeviceDetail">测试 DeviceDetail</a-button>
        <a-button @click="testCustom">测试自定义方法</a-button>
        <a-button @click="clearData">清空数据</a-button>
      </a-space>
    </div>

    <div class="demo-section">
      <h3>当前设备信息：</h3>
      <p v-if="currentDevice">设备ID: {{ currentDevice.id }}, 设备名: {{ currentDevice.name }}</p>
      <p v-else>暂无设备信息</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useUEListener, createCommonListeners } from '@/composables/useUEListener';

// 响应式数据
const receivedData = ref<Array<{method: string, data: any, time: string}>>([]);
const currentDevice = ref<{id: string, name: string} | null>(null);

// 使用 UE 监听器
const { addListener } = useUEListener();
const commonListeners = createCommonListeners();

// 添加数据到显示列表
const addDataToDisplay = (method: string, data: any) => {
  receivedData.value.unshift({
    method,
    data: JSON.stringify(data),
    time: new Date().toLocaleTimeString()
  });
  
  // 只保留最新的10条数据
  if (receivedData.value.length > 10) {
    receivedData.value = receivedData.value.slice(0, 10);
  }
};

// 设置监听器
commonListeners.onInfoPage((data) => {
  console.log('收到 InfoPage 数据:', data);
  addDataToDisplay('InfoPage', data);
});

commonListeners.onDeviceDetail((data) => {
  console.log('收到 DeviceDetail 数据:', data);
  addDataToDisplay('DeviceDetail', data);
  
  // 更新当前设备信息
  if (data && data.deviceId) {
    currentDevice.value = {
      id: data.deviceId,
      name: data.deviceName || '未知设备'
    };
  }
});

commonListeners.onPageSwitch((data) => {
  console.log('收到 PageSwitch 数据:', data);
  addDataToDisplay('PageSwitch', data);
});

commonListeners.onDeviceStatus((data) => {
  console.log('收到 DeviceStatus 数据:', data);
  addDataToDisplay('DeviceStatus', data);
});

commonListeners.onAlarm((data) => {
  console.log('收到 AlarmNotification 数据:', data);
  addDataToDisplay('AlarmNotification', data);
});

// 自定义监听器示例
addListener('CustomMethod', (data) => {
  console.log('收到 CustomMethod 数据:', data);
  addDataToDisplay('CustomMethod', data);
});

// 测试方法（模拟 UE 调用）
const testInfoPage = () => {
  if (window.ueInterface?.InfoPage) {
    window.ueInterface.InfoPage({
      type: 'test',
      message: '这是测试的 InfoPage 数据',
      timestamp: Date.now()
    });
  }
};

const testDeviceDetail = () => {
  if (window.ueInterface?.DeviceDetail) {
    window.ueInterface.DeviceDetail({
      deviceId: 'device_001',
      deviceName: '测试设备',
      status: 'online',
      temperature: 25.5
    });
  }
};

const testCustom = () => {
  if (window.ueInterface?.CustomMethod) {
    window.ueInterface.CustomMethod({
      customData: '这是自定义方法的数据',
      value: Math.random()
    });
  }
};

const clearData = () => {
  receivedData.value = [];
  currentDevice.value = null;
};
</script>

<style scoped>
.ue-listener-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.data-display {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.data-item {
  padding: 8px;
  margin-bottom: 8px;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.data-item strong {
  color: #1890ff;
}

.data-item small {
  float: right;
  color: #999;
}
</style>
