if (typeof ue != "object" || typeof ue.interface != "object") {
  if (typeof ue != "object") ue = {};
  // mobile
  ue.interface = {};
  ue.interface.broadcast = function (name, data) {
    if (typeof name != "string") return;
    var args = [name, ""];
    if (typeof data != "undefined") args[1] = data;
    var hash = encodeURIComponent(JSON.stringify(args));
    if (typeof history == "object" && typeof history.pushState == "function") {
      history.pushState({}, "", "#" + hash);
      history.pushState({}, "", "#" + encodeURIComponent("[]"));
    } else {
      document.location.hash = hash;
      document.location.hash = encodeURIComponent("[]");
    }
  };
} else
  (function (obj) {
    // desktop
    ue.interface = {};
    ue.interface.broadcast = function (name, data) {
      if (typeof name != "string") return;
      if (typeof data != "undefined") obj.broadcast(name, JSON.stringify(data));
      else obj.broadcast(name, "");
    };
  })(ue.interface);

// 将 ue.interface 挂载到 window 上
window.ue5 = ue.interface.broadcast;
window.ueInterface = ue.interface;

// 创建一个简单的监听器管理系统
window.ueListeners = {};

// 添加监听器的方法
window.addUEListener = function (methodName, callback) {
  if (!window.ueListeners[methodName]) {
    window.ueListeners[methodName] = [];

    // 在 ue.interface 上创建这个方法
    ue.interface[methodName] = function (data) {
      console.log("UE调用:", methodName, data);
      // 调用所有注册的监听器
      window.ueListeners[methodName].forEach(function (cb) {
        try {
          cb(data);
        } catch (error) {
          console.error("监听器执行错误:", error);
        }
      });
    };
  }

  // 添加回调函数到列表
  window.ueListeners[methodName].push(callback);
  // console.log("已添加监听器:", methodName);
};

// 移除监听器的方法
window.removeUEListener = function (methodName, callback) {
  if (window.ueListeners[methodName]) {
    var index = window.ueListeners[methodName].indexOf(callback);
    if (index > -1) {
      window.ueListeners[methodName].splice(index, 1);
      console.log("已移除监听器:", methodName);
    }
  }
};
