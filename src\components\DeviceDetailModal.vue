<template>
  <a-modal
    v-model:visible="visible"
    :title="`${deviceInfo?.name || '设备'}详情`"
    width="1200px"
    :footer="null"
    :destroyOnClose="true"
    @cancel="handleClose"
    class="global-device-detail-modal"
  >
    <div class="device-detail-modal">
      <a-tabs v-model:activeKey="activeTab" type="card" size="large" @change="handleTabChange">
        <!-- 设备信息 Tab -->
        <a-tab-pane key="info" tab="设备信息">
          <div class="device-info-content">
            <div>
              <img src="@/assets/pages-icon/iot-facilities/fjpg.png" alt="" />
            </div>
            <a-spin :spinning="loading.info">
              <ul v-if="deviceInfo" class="info-list">
                <li v-for="(item, index) in deviceInfoList" :key="index">
                  <div class="item-title">{{ item.title }}</div>
                  <div class="item-list">
                    <div class="info-item" v-for="info in item.list" :key="info.key">
                      <span class="info-label">{{ info.label }}：</span>
                      <span class="info-value">{{ info.value }}</span>
                    </div>
                  </div>
                </li>
              </ul>
              <a-empty v-else description="暂无设备信息" />
            </a-spin>
          </div>
        </a-tab-pane>

        <!-- 数据查询 Tab -->
        <a-tab-pane key="data" tab="数据查询">
          <div class="data-query-content">
            <!-- 查询条件 -->
            <SearchForm :fields="searchFields" :initialValues="initialValues" />

            <!-- 图表区域 -->
            <div class="chart-area">
              <a-spin :spinning="loading.data">
                <common-chart
                  v-if="logValues.length > 0"
                  :echart-obj="chartOption"
                  style="height: 500px"
                />
                <a-empty v-else description="暂无数据" />
              </a-spin>
            </div>
          </div>
        </a-tab-pane>

        <!-- 系统图 Tab -->
        <a-tab-pane key="diagram" tab="系统图">
          <div class="diagram-content">
            <a-spin :spinning="loading.diagram">
              <div v-if="systemDiagram" class="diagram-container">
                <img
                  :src="systemDiagram"
                  :alt="`${deviceInfo?.name}系统图`"
                  class="diagram-image"
                  @error="handleImageError"
                />
              </div>
              <a-empty v-else description="暂无系统图" />
            </a-spin>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import { message } from "ant-design-vue";
import CommonChart from "@/components/CommonChart.vue";
import SearchForm from "@/components/SearchForm.vue";
import dayjs from "dayjs";
import { getDeviceData, getQueryParams } from "@/api/device";

interface DeviceInfo {
  id: string;
  name: string;
  type: string;
  model: string;
  manufacturer: string;
  installDate: string;
  location: string;
  status: string;
  power: number;
  voltage: number;
  current: number;
  [key: string]: any;
}
// Props 定义
interface Props {
  deviceId?: string;
  modelValue?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  deviceId: "",
  modelValue: false,
});

// Emits 定义
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  close: [];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});
// 监听设备ID变化
watch(
  () => props.deviceId,
  (newId) => {
    if (newId && visible.value) {
      loadDeviceData(newId);
    }
  },
  { immediate: true },
);

// 监听弹窗显示状态
watch(visible, (newVisible) => {
  if (newVisible && props.deviceId) {
    loadDeviceData(props.deviceId);
  } else if (!newVisible) {
    resetData();
  }
});
const activeTab = ref("data");
const deviceInfo = ref<DeviceInfo | null>(null);
const systemDiagram = ref<string>("");

// 加载状态
const loading = reactive({
  info: false,
  data: false,
  diagram: false,
});

const searchFields = ref([
  {
    type: "rangePicker" as const,
    prop: "queryDate",
    formItem: { label: "查询时间" },
    span: 12,
    attrs: {
      size: "large",
      placeholder: ["开始时间", "结束时间"],
      valueFormat: "YYYY-MM-DD HH:mm:ss",
      format: "YYYY-MM-DD HH:mm:ss,YYYY-MM-DD HH:mm:ss",
    },
    on: {
      change: (val: any) => {
        console.log("%c Line:201 🍩 val", "color:#fca650", val);
        initialValues.value.queryDate = val;
        loadChartDate();
      },
    },
  },
  {
    type: "select" as const,
    prop: "fieldId",
    formItem: { label: "参数" },
    span: 10,
    attrs: {
      placeholder: "请选择参数",
      clearable: true,
      size: "large",
    },
    options: [],
    on: {
      change: (val: any) => {
        initialValues.value.fieldId = val;
        loadChartDate();
      },
    },
  },
]);

const initialValues = ref({
  queryDate: [],
  fieldId: "",
});

const handleTabChange = (key: string) => {
  if (key === "data") {
    console.log("%c Line:207 🍬 key", "color:#ffdd4d", key);
    // loadChartDate();
  }
}; // 设备信息表格数据
const deviceInfoList = computed(() => {
  if (!deviceInfo.value) return [];
  return [
    {
      title: "基本信息",
      list: [
        { key: "name", label: "设备名称", value: deviceInfo.value.name },
        { key: "type", label: "设备类型", value: deviceInfo.value.type },
        { key: "model", label: "设备型号", value: deviceInfo.value.model },
        { key: "manufacturer", label: "制造商", value: deviceInfo.value.manufacturer },
        { key: "installDate", label: "安装日期", value: deviceInfo.value.installDate },
        { key: "location", label: "安装位置", value: deviceInfo.value.location },
        { key: "status", label: "运行状态", value: deviceInfo.value.status },
        { key: "power", label: "额定功率", value: `${deviceInfo.value.power}kW` },
        { key: "voltage", label: "额定电压", value: `${deviceInfo.value.voltage}V` },
        { key: "current", label: "额定电流", value: `${deviceInfo.value.current}A` },
      ],
    },
    {
      title: "实时监测",
      list: [
        { key: "name", label: "设备名称", value: deviceInfo.value.name },
        { key: "type", label: "设备类型", value: deviceInfo.value.type },
      ],
    },
  ];
});
// 图表数据
const chartOption = ref<any>({
  title: {
    text: "设备数据趋势",
    left: "center",
    textStyle: {
      color: "#fff",
    },
  },
  tooltip: {
    trigger: "axis",
    backgroundColor: "rgba(0, 10, 20, 0.8)",
    borderColor: "rgba(255, 255, 255, 0.2)",
    textStyle: {
      color: "#fff",
    },
  },
  legend: {
    data: [],
    textStyle: {
      color: "#fff",
    },
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "15%",
    containLabel: true,
  },
  dataZoom: [
    {
      type: "slider",
      startValue: 90,
      endValue: 100,
      minValueSpan: 5, // 最小显示的柱子数量
    },
  ],
  xAxis: {
    type: "category",
    boundaryGap: false,
    data: [],
    axisLine: {
      lineStyle: {
        color: "#fff",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
      // 标签格式化函数，处理过长的标签
      formatter: function (value: string) {
        if (value.length > 8) {
          return value.substring(11, 16); // 超过5个字符显示省略号
        }
        return value;
      },
    },
  },
  yAxis: {
    name: "单位：Kw",
    type: "value",

    min: function (value: any) {
      // 向上取整
      return Math.ceil(value.min - 10);
    },
    max: function (value: any) {
      // 向下取整
      return Math.floor(value.max + 10);
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 1)",
      },
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        // type: "dashed",
      },
    },
  },
  series: [],
});
// 加载设备数据
const loadDeviceData = async (_deviceId: string) => {
  await Promise.all([loadDeviceInfo(), loadSystemDiagram(), loadQueryParams()]);
};
// 查询参数获取
const loadQueryParams = async () => {
  try {
    const res = await getQueryParams(props.deviceId);
    if (res.code === 200) {
      searchFields.value[1].options = res.data.map((item: any) => ({
        label: item.label,
        value: item.fieldId,
      }));
      initialValues.value.fieldId = res.data[0].fieldId || res.data[0].fieldIid;
      loadChartDate();
    }
  } catch (error) {
    console.error("Query params error:", error);
  }
};
// 图表数据查询
const loadChartDate = async () => {
  try {
    loading.data = true;
    const params = {
      smsLocation: props.deviceId, //设备模型对应编码
      startTime: initialValues.value.queryDate[0] || "", //开始时间
      endTime: initialValues.value.queryDate[1] || "", //结束时间
      fieldId: initialValues.value.fieldId, //参数
    };
    const res = await getDeviceData(params);
    updateChartData(res.data);
  } catch (error) {
    console.error("Query data error:", error);
  } finally {
    loading.data = false;
  }
};
// 加载设备信息
const loadDeviceInfo = async () => {
  try {
    loading.info = true;

    // 尝试从API获取数据，失败时使用Mock数据
    try {
      // const res = await getDeviceInfo(deviceId);
      // deviceInfo.value = res.data;
      // deviceInfo.value = generateMockDeviceInfo(deviceId);
    } catch (apiError) {
      console.warn("API not available, using mock data:", apiError);
      // 使用Mock数据
      // deviceInfo.value = generateMockDeviceInfo(deviceId);
    }
  } catch (error) {
    message.error("获取设备信息失败");
    console.error("Load device info error:", error);
  } finally {
    loading.info = false;
  }
};

// 加载系统图
const loadSystemDiagram = async () => {
  try {
    loading.diagram = true;

    // 尝试从API获取数据，失败时使用Mock数据
    try {
      // const response = await getSystemDiagram(props.deviceId);
      // systemDiagram.value = response.data.imageUrl;
      systemDiagram.value = "https://via.placeholder.com/800x600/1f1f1f/ffffff?text=系统图示例";
    } catch (apiError) {
      console.warn("API not available, using mock diagram:", apiError);
      // 使用Mock系统图（这里可以放一个示例图片URL）
      systemDiagram.value = "https://via.placeholder.com/800x600/1f1f1f/ffffff?text=系统图示例";
    }
  } catch (error) {
    message.error("获取系统图失败");
    console.error("Load system diagram error:", error);
  } finally {
    loading.diagram = false;
  }
};

// 更新图表数据
let logValues: any[] = [];
const updateChartData = (data: any = []) => {
  // 反转数组数据
  data = data.reverse();
  logValues = data.map((item: any) => Number(item.logValue).toFixed(2));
  // "2025-08-08 10:36:05"  截取小时分钟 格式为 YYYY-MM-DD HH:mm
  const timeFormat = "YYYY-MM-DD HH:mm";
  const times = data.map((item: any) => dayjs(item.createTime).format(timeFormat));
  // const times = data.map((item: any) => item.createTime);
  chartOption.value = {
    ...chartOption.value,
    // title: {
    //   ...chartOption.value.title,
    //   text: `${deviceInfo.value?.name || "设备"} - ${getDataTypeName(
    //     initialValues.value.dataType,
    //   )}趋势`,
    // },
    xAxis: {
      ...chartOption.value.xAxis,
      data: times,
    },
    series: [
      {
        // name: getDataTypeName(initialValues.value.dataType),
        type: "line",
        showSymbol: false, // 不显示折线图拐点
        // symbolSize: 8, // 拐点大小
        data: logValues,
      },
    ],
  };
};

// 获取数据类型名称
// const getDataTypeName = (type: string) => {
//   const typeMap: Record<string, string> = {
//     power: "功率(kW)",
//     energy: "能耗(kWh)",
//     voltage: "电压(V)",
//     current: "电流(A)",
//   };
//   return typeMap[type] || type;
// };

// 处理图片加载错误
const handleImageError = () => {
  message.error("系统图加载失败");
};

// 关闭弹窗
const handleClose = () => {
  emit("close");
  visible.value = false;
};

// 重置数据
const resetData = () => {
  activeTab.value = "info";
  deviceInfo.value = null;
  systemDiagram.value = "";
  // queryForm.dateRange = null;
  // queryForm.dataType = "power";
  chartOption.value.series = [];
};
</script>

<style lang="less" scoped>
.device-detail-modal {
  min-height: 700px;
  .device-info-content {
    display: flex;
    padding: 20px;
    color: #fff;
    > div {
      flex: 1;
      max-width: 50%;
      img {
        max-width: 90%;
      }
    }

    .info-list {
      .item-title {
        height: 50px;
        line-height: 70px;
      }
      .item-title::before {
        content: "";
        display: inline-block;
        width: 5px;
        height: 5px;
        background: #00a0e9;
        // border-radius: 50%;
        margin-right: 10px;
        margin-bottom: 3px;
      }
      .item-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0px;
        .info-item {
          display: flex;
          align-items: center;
          .info-label {
            padding: 5px;
            border: 1px solid #00a0e9;
            flex: 1;
          }
          .info-value {
            border: 1px solid #00a0e9;
            flex: 1;
            padding: 5px;
          }
        }
      }
    }
  }

  .data-query-content {
    .query-form {
      padding: 20px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 6px;
      margin-bottom: 20px;
    }

    .chart-area {
      padding: 20px;
      border-radius: 6px;
    }
  }

  .diagram-content {
    padding: 20px;
    text-align: center;

    .diagram-container {
      .diagram-image {
        max-width: 100%;
        max-height: 600px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
    }
  }
}
// :deep(.ant-form-item-label > label) {
//   color: rgba(255, 255, 255, 0.8);
// }

// :deep(.ant-empty-description) {
//   color: rgba(255, 255, 255, 0.6);
// }
</style>
<style lang="less">
.ant-modal-root {
  .ant-modal-mask {
    background: rgba(#fff, 0.7);
  }
  .global-device-detail-modal {
    // Modal样式更改
    .ant-modal-content,
    .ant-modal-header {
      background: #001a28cc;
      color: #fff;
      .ant-modal-title {
        color: #fff;
      }
    }
    //tab 样式
    .ant-tabs-card {
      & > .ant-tabs-nav::before {
        display: none;
      }
      .ant-tabs-tab {
        background: #cdcdcd4d;
        border-color: rgba(255, 255, 255, 0.2);
        color: #fff;
        &.ant-tabs-tab-active {
          .ant-tabs-tab-btn {
            color: #fff;
          }
          background: #0052b2;
        }
      }
    }
    .ant-empty-description,
    .ant-picker-separator {
      color: #fff;
    }
  }
}
</style>
