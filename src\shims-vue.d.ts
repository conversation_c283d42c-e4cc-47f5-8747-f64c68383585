/* eslint-disable */
declare module "*.vue" {
  import type { DefineComponent } from "vue";
  const component: DefineComponent<{}, {}, any>;
  export default component;
}
declare interface Window {
  apiHost: any;
  signallingApiHost: any;
  energyApiHost: any;
  ue5: any;
  ueInterface?: any;
  // UE 监听器相关
  ueListeners: any;
  addUEListener: (methodName: string, callback: (data: any) => void) => void;
  removeUEListener: (methodName: string, callback: (data: any) => void) => void;
}

declare module "*.svg" {
  const value: any;
  export default value;
}
declare module "*.png" {
  const value: any;
  export default value;
}
