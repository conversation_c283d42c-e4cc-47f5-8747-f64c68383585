<template>
  <div class="login-container">
    <div class="login-form">
      <h2>系统登录</h2>
      <a-form :model="loginForm" :rules="rules" @finish="handleLogin" layout="vertical">
        <a-form-item label="用户名" name="username">
          <a-input v-model:value="loginForm.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item label="密码" name="password">
          <a-input-password v-model:value="loginForm.password" placeholder="请输入密码" />
        </a-form-item>
        <a-form-item>
          <a-button class="login-button" html-type="submit" :loading="loading" block>登录</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { message } from "ant-design-vue";
import { login } from "@/api/auth";
import { getBuildFloor } from "@/api/global";

const router = useRouter();
const store = useStore();
const loading = ref(false);

const loginForm = reactive({
  username: "NQI",
  password: "nqi!@#123",
});

const rules = {
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
};

const handleLogin = async () => {
  loading.value = true;
  try {
    const response = await login(loginForm);

    // 保存token到localStorage
    localStorage.setItem("token", response.token);
    const buildRes = await getBuildFloor();
    console.log("%c Line:50 🍩 buildRes", "color:#6ec1c2", buildRes);

    // 更新store中的登录状态和用户信息
    store.commit("setLoginStatus", true);
    // store.commit("setUserInfo", response.user);

    message.success(response.msg);
    router.push("/");
  } catch (error: any) {
    message.error(error.message || "登录失败");
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped lang="less">
.login-container {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  pointer-events: auto;
}

.login-form {
  width: 700px;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }
  .login-button {
    background: #0070d9;
    border-color: #0070d9;
    color: white;
  }
}
</style>
