<template>
  <a-layout :class="[theme, 'app-wrapper ht100']">
    <!-- <web-rtc v-show="currentWebRtcFlag" /> -->
    <div class="app-main-container ht100">
      <div class="menu-header-container">
        <menu-header></menu-header>
      </div>
      <div class="layout-content">
        <app-main class="content-main" />
        <!-- <tool-control-area />
        <video-digital />
        <menu-legend v-if="menuLegendVisible" />
        <over-menu-legend v-else-if="overMenuLegendVisible" /> -->
      </div>
    </div>
  </a-layout>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, provide, computed, getCurrentInstance, nextTick } from "vue";

import AppMain from "@/layout/components/AppMain.vue";
import MenuHeader from "@/layout/components/MenuHeader.vue";
import resizeBus from "@/layout/mixin/ResizeHandler";
// import WebRtc from "@/components/WebRtc.vue";
// import MenuLegend from "@/layout/components/MenuLegend.vue";
// import OverMenuLegend from "@/layout/components/OverMenuLegend.vue";
// import ToolControlArea from "@/layout/components/ToolControlArea.vue";
// import VideoDigital from "@/layout/components/VideoDigital.vue";
// import { useRouter } from "vue-router";
import { useStore } from "vuex";
// const router = useRouter();
const store = useStore();
// const currentRoute = router.currentRoute;
const ins = getCurrentInstance();

const bus = ins.appContext.config.globalProperties.$bus;
// 把bus 作为一个方法对象为全局组建提供
provide("bus", bus);
// const currentWebRtcFlag = computed(
//   () => !!currentRoute.value.meta.hasWebRtc || store.state.webRtcVisible,
// );
// const menuLegendVisible = computed(() => !currentRoute.value.meta.disabledMenuLegend);
// const overMenuLegendVisible = computed(() => !currentRoute.value.meta.disabledOverMenuLegend);
const theme = computed(() => store.state.theme);

onMounted(() => {
  resizeBus.init(() => {
    store.commit("initSreenSize", {
      width: document.documentElement.clientWidth,
      height: document.documentElement.clientHeight,
      flag: document.documentElement.clientWidth > 1400,
    });
  });
});

onUnmounted(() => {
  resizeBus.destory();
});

// 监听页面信息
import { createCommonListeners } from "@/composables/useUEListener";
import { showDeviceDetail } from "@/utils/deviceModal";

const listeners = createCommonListeners();
listeners.onInfoPage((data) => {
  console.log("%c Line:67 🍧 data", "color:#fca650", data);
  if (data.BIMID) {
    showDeviceDetail(data.BIMID);
  }
});
</script>

<style scoped lang="less">
.app-wrapper {
  position: relative;
  // font-family: "HarmonyOS Sans SC";
  font-style: normal;
  font-weight: 500;
  background: var(--wrapper-bg);
  .layout-content {
    display: flex;
    flex-direction: row;
    padding-top: 150px;
  }
  .app-main-container {
    pointer-events: none;
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
  }
  .content-main {
    background: transparent;
    width: 100%;
    height: 100%;
    z-index: 9;
    flex: auto;
  }
  .menu-header-container {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 11;
  }
}
</style>
